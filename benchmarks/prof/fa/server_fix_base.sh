#!/bin/bash

# ==================== 通用Profiling脚本 ====================
# 使用方法:
# 1. 复制 config_template.sh 为你的配置文件
# 2. 修改配置文件中的参数
# 3. 运行: ./server_dynamic_base.sh [config_file]
#
# 示例: ./server_dynamic_base.sh my_config.sh
# 如果不指定配置文件，将使用默认配置

# ==================== 加载配置 ====================
CONFIG_FILE="${1:-config_template.sh}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

if [ -f "${SCRIPT_DIR}/${CONFIG_FILE}" ]; then
    echo "加载配置文件: ${CONFIG_FILE}"
    source "${SCRIPT_DIR}/${CONFIG_FILE}"
elif [ -f "${CONFIG_FILE}" ]; then
    echo "加载配置文件: ${CONFIG_FILE}"
    source "${CONFIG_FILE}"
else
    echo "配置文件不存在: ${CONFIG_FILE}"
    echo "请确保配置文件存在，或使用默认配置模板"
    echo "使用方法: $0 [config_file]"
    exit 1
fi

# 验证必要的配置变量
required_vars=(
    "VLLM_PATH" "BENCHMARK_SCRIPT"
    "SERVER1_MODEL_NAME" "SERVER1_MODEL_PATH" "SERVER1_TOKENIZER_PATH"
    "SERVER2_MODEL_NAME" "SERVER2_MODEL_PATH" "SERVER2_TOKENIZER_PATH"
    "SERVER1_PORT" "SERVER2_PORT" "SERVER1_GPU_DEVICES" "SERVER2_GPU_DEVICES"
    "SERVER1_TENSOR_PARALLEL" "SERVER2_TENSOR_PARALLEL" "SERVER1_DTYPE" "SERVER2_DTYPE"
    "SERVER1_TORCH_PROFILER_DIR" "SERVER2_TORCH_PROFILER_DIR"
    "BASE_PROF_DIR" "CHUNK_PROF_DIR" "BASE_LOG_FILE" "CHUNK_LOG_FILE"
    "BASE_TEST_CASES" "CHUNK_TEST_CASES"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "错误: 配置变量 $var 未设置"
        exit 1
    fi
done

echo "配置验证通过，开始执行..."

# ==================== 锁文件机制 ====================
LOCKFILE="/tmp/prof_llama_test.lock"
if [ -f "$LOCKFILE" ]; then
    echo "脚本已在运行中 (锁文件: $LOCKFILE)，退出..."
    exit 1
fi
echo $$ > "$LOCKFILE"

# 任务级别的锁文件
BASE_TASK_LOCK="/tmp/prof_base_task.lock"
CHUNK_TASK_LOCK="/tmp/prof_chunk_task.lock"

# 清理函数
cleanup() {
    echo "清理锁文件..."
    rm -f "$LOCKFILE" "$BASE_TASK_LOCK" "$CHUNK_TASK_LOCK"
    exit
}
trap cleanup EXIT INT TERM
# ==================== 启动服务器 ====================
echo "启动服务器..."

# 创建profiler目录
mkdir -p "${SERVER1_TORCH_PROFILER_DIR}"
mkdir -p "${SERVER2_TORCH_PROFILER_DIR}"

# 启动第一个服务器 (Base)
nohup bash -c "HIP_VISIBLE_DEVICES=${SERVER1_GPU_DEVICES} VLLM_FLASH_ATTN_V1=1 PYTHONPATH=${VLLM_PATH} VLLM_USE_FLASH_ATTN_PA=${VLLM_USE_FLASH_ATTN_PA} VLLM_TORCH_PROFILER_DIR=${SERVER1_TORCH_PROFILER_DIR} python -m vllm.entrypoints.openai.api_server --model ${SERVER1_MODEL_PATH} -tp ${SERVER1_TENSOR_PARALLEL} --dtype ${SERVER1_DTYPE} --served-model-name ${SERVER1_MODEL_NAME} --block-size ${SERVER1_BLOCK_SIZE} --port ${SERVER1_PORT} ${SERVER1_EXTRA_ARGS}" >${SERVER1_LOG_FILE} 2>&1 &
SERVER1_PID=$!

# 启动第二个服务器 (Chunk)
nohup bash -c "HIP_VISIBLE_DEVICES=${SERVER2_GPU_DEVICES} VLLM_FLASH_ATTN_V1=1 PYTHONPATH=${VLLM_PATH} VLLM_USE_FLASH_ATTN_PA=${VLLM_USE_FLASH_ATTN_PA} VLLM_TORCH_PROFILER_DIR=${SERVER2_TORCH_PROFILER_DIR} python -m vllm.entrypoints.openai.api_server --model ${SERVER2_MODEL_PATH} -tp ${SERVER2_TENSOR_PARALLEL} --dtype ${SERVER2_DTYPE} --served-model-name ${SERVER2_MODEL_NAME} --block-size ${SERVER2_BLOCK_SIZE} --port ${SERVER2_PORT} ${SERVER2_EXTRA_ARGS}" >${SERVER2_LOG_FILE} 2>&1 &
SERVER2_PID=$!

# 等待服务就绪
echo "等待服务器启动..."
while ! curl -s --head http://localhost:${SERVER1_PORT}/v1/models > /dev/null; do sleep 2; done
while ! curl -s --head http://localhost:${SERVER2_PORT}/v1/models > /dev/null; do sleep 2; done
echo "服务器启动完成"
# ==================== 执行测试 ====================
# 并行运行测试 - Base任务
(
    # 获取任务锁
    exec 200>"$BASE_TASK_LOCK"
    if ! flock -n 200; then
        echo "Base任务已在运行中，退出..."
        exit 1
    fi

    echo "开始Base任务测试..."
    mkdir -p "${BASE_PROF_DIR}"

    for case in "${BASE_TEST_CASES[@]}"; do
        read -r input_len num_prompts <<<"$case"
        echo "开始Base测试用例: ${num_prompts} prompts, ${input_len} tokens - $(date)"

        # 清理旧的 profiler 文件，避免计数错误
        rm -f ${SERVER1_TORCH_PROFILER_DIR}/*.gz 2>/dev/null || true
        echo "Base任务清理完成，当前文件数: $(ls ${SERVER1_TORCH_PROFILER_DIR}/*.gz 2>/dev/null | wc -l)"

        # 标注分隔行到日志
        echo "${num_prompts}-${input_len}" >> "${BASE_LOG_FILE}"

        # 执行benchmark
        HIP_VISIBLE_DEVICES=${SERVER1_GPU_DEVICES} python3 ${BENCHMARK_SCRIPT} \
            --model ${SERVER1_MODEL_NAME} \
            --tokenizer ${SERVER1_TOKENIZER_PATH} \
            --random-input-len "${input_len}" \
            --random-output-len ${BASE_OUTPUT_LEN} \
            --num-prompts "${num_prompts}" \
            --max-concurrency "${num_prompts}" \
            ${IGNORE_EOS} \
            --dataset-name ${DATASET_NAME} \
            --port ${SERVER1_PORT} \
            ${PROFILE_FLAG} \
            >> "${BASE_LOG_FILE}" 2>&1
        echo "Base benchmark完成，等待profiler文件生成..."
        sleep ${SLEEP_AFTER_BENCHMARK}

        OUT_DIR="${BASE_PROF_DIR}/${num_prompts}_${input_len}"
        elapsed=0
        while true; do
            count=$(ls ${SERVER1_TORCH_PROFILER_DIR}/*.gz 2>/dev/null | wc -l)
            echo "$(date): Base任务找到 ${count}/${BASE_EXPECTED_FILES} 个profiler文件"
            if [ "$count" -ge ${BASE_EXPECTED_FILES} ]; then
                echo "Base任务找到足够文件，退出等待循环"
                break
            fi
            if [ $elapsed -ge ${PROFILER_TIMEOUT} ]; then
                echo "Base任务超时退出: 等待${PROFILER_TIMEOUT}秒后仍只有${count}个文件"
                ls -la ${SERVER1_TORCH_PROFILER_DIR}/
                break
            fi
            sleep ${SLEEP_BETWEEN_CHECKS}
            elapsed=$((elapsed + SLEEP_BETWEEN_CHECKS))
        done

        # 移动文件从profiler目录到最终输出目录
        if [ "$count" -ge ${BASE_EXPECTED_FILES} ]; then
            mkdir -p "${OUT_DIR}"
            if ls ${SERVER1_TORCH_PROFILER_DIR}/*.gz >/dev/null 2>&1; then
                mv ${SERVER1_TORCH_PROFILER_DIR}/*.gz "${OUT_DIR}"/
                echo "Base任务文件移动完成: $(ls ${OUT_DIR}/*.gz | wc -l) 个文件从 ${SERVER1_TORCH_PROFILER_DIR} 到 ${OUT_DIR}"
            else
                echo "Base任务警告: 虽然计数为${count}，但没找到.gz文件"
            fi
        else
            echo "Base任务跳过移动: 文件数量不足(${count}/${BASE_EXPECTED_FILES})"
        fi
        echo "Base测试用例 ${num_prompts}-${input_len} 完成"
        echo "----------------------------------------"
    done

    # 释放锁
    flock -u 200
) &
# TEST1_PID=$!
# 并行运行测试 - Chunk任务
(
    # 获取任务锁
    exec 201>"$CHUNK_TASK_LOCK"
    if ! flock -n 201; then
        echo "Chunk任务已在运行中，退出..."
        exit 1
    fi

    echo "开始Chunk任务测试..."
    mkdir -p "${CHUNK_PROF_DIR}"

    for case in "${CHUNK_TEST_CASES[@]}"; do
        read -r input_len num_prompts <<<"$case"
        echo "开始Chunk测试用例: ${num_prompts} prompts, ${input_len} tokens - $(date)"

        # 清理旧的 profiler 文件，避免计数错误
        rm -f ${SERVER2_TORCH_PROFILER_DIR}/*.gz 2>/dev/null || true
        echo "Chunk任务清理完成，当前文件数: $(ls ${SERVER2_TORCH_PROFILER_DIR}/*.gz 2>/dev/null | wc -l)"

        # 标注分隔行到日志
        echo "${num_prompts}-${input_len}" >> "${CHUNK_LOG_FILE}"

        # 执行benchmark
        HIP_VISIBLE_DEVICES=${SERVER2_GPU_DEVICES} python3 ${BENCHMARK_SCRIPT} \
            --model ${SERVER2_MODEL_NAME} \
            --tokenizer ${SERVER2_TOKENIZER_PATH} \
            --random-input-len "${input_len}" \
            --random-output-len ${CHUNK_OUTPUT_LEN} \
            --num-prompts ${num_prompts} \
            --max-concurrency ${num_prompts} \
            ${IGNORE_EOS} \
            --dataset-name ${DATASET_NAME} \
            --port ${SERVER2_PORT} \
            ${PROFILE_FLAG} \
            >> "${CHUNK_LOG_FILE}" 2>&1
        echo "Chunk benchmark完成，等待profiler文件生成..."
        sleep ${SLEEP_AFTER_CHUNK_BENCHMARK}

        OUT_DIR="${CHUNK_PROF_DIR}/${num_prompts}_${input_len}"
        elapsed=0
        while true; do
            count=$(ls ${SERVER2_TORCH_PROFILER_DIR}/*.gz 2>/dev/null | wc -l)
            echo "$(date): Chunk任务找到 ${count}/${CHUNK_EXPECTED_FILES} 个profiler文件"
            if [ "$count" -ge ${CHUNK_EXPECTED_FILES} ]; then
                echo "Chunk任务找到足够文件，退出等待循环"
                break
            fi
            if [ $elapsed -ge ${PROFILER_TIMEOUT} ]; then
                echo "Chunk任务超时退出: 等待${PROFILER_TIMEOUT}秒后仍只有${count}个文件"
                ls -la ${SERVER2_TORCH_PROFILER_DIR}/
                break
            fi
            sleep ${SLEEP_BETWEEN_CHECKS}
            elapsed=$((elapsed + SLEEP_BETWEEN_CHECKS))
        done

        # 移动文件从profiler目录到最终输出目录
        if [ "$count" -ge ${CHUNK_EXPECTED_FILES} ]; then
            mkdir -p "${OUT_DIR}"
            # 检查文件是否存在，避免mv错误
            if ls ${SERVER2_TORCH_PROFILER_DIR}/*.gz >/dev/null 2>&1; then
                mv ${SERVER2_TORCH_PROFILER_DIR}/*.gz "${OUT_DIR}"/
                echo "Chunk任务文件移动完成: $(ls ${OUT_DIR}/*.gz | wc -l) 个文件从 ${SERVER2_TORCH_PROFILER_DIR} 到 ${OUT_DIR}"
            else
                echo "Chunk任务警告: 虽然计数为${count}，但没找到.gz文件"
            fi
        else
            echo "Chunk任务跳过移动: 文件数量不足(${count}/${CHUNK_EXPECTED_FILES})"
        fi
        echo "Chunk测试用例 ${num_prompts}-${input_len} 完成"
        echo "----------------------------------------"
    done

    # 释放锁
    flock -u 201
) &
TEST2_PID=$!

# ==================== 等待完成并清理 ====================
echo "等待所有测试完成..."
# wait $TEST1_PID  # 如果需要等待Base任务，取消注释
wait $TEST2_PID

echo "测试完成，停止服务器..."
# 停止服务
kill $SERVER1_PID 2>/dev/null || true
kill $SERVER2_PID 2>/dev/null || true

echo "脚本执行完成"