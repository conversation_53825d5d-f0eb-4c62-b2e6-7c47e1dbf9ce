#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import csv

def process_log_to_csv_with_blanks(log_file: str) -> str:
    """
    将单个 .log 文件转换为 CSV，并在每个批次段前插入空行。
    """
    csv_file = log_file.replace('.log', '.csv')
    results = []

    # 正则模板
    run_re = re.compile(
        r"实例 \d+: num-prompts=(\d+), max-concurrency=\d+, input-len=(\d+), output-len=(\d+)"
    )
    total_token_re  = re.compile(r"Total Token throughput \(tok/s\):\s+([\d\.]+)")
    output_token_re = re.compile(r"Output token throughput \(tok/s\):\s+([\d\.]+)")
    mean_ttft_re    = re.compile(r"Mean TTFT \(ms\):\s+([\d\.]+)")
    mean_tpot_re    = re.compile(r"Mean TPOT \(ms\):\s+([\d\.]+)")

    last_bs = None
    first_record_seen = False

    # 读取文件
    with open(log_file, 'r', encoding='utf-8') as f:        # 利用上下文管理器保证文件正确关闭
        lines = f.readlines()

    i = 0
    while i < len(lines):
        m = run_re.search(lines[i])
        if m:
            num_prompts, input_len, output_len = m.groups()
            total_token = output_token = mean_ttft = mean_tpot = ''

            # 在后续 100 行内抓取统计指标
            for j in range(i, min(i + 1000, len(lines))):
                if not total_token:
                    m1 = total_token_re.search(lines[j])
                    if m1:
                        total_token = m1.group(1)
                if not output_token:
                    m2 = output_token_re.search(lines[j])
                    if m2:
                        output_token = m2.group(1)
                if not mean_ttft:
                    m3 = mean_ttft_re.search(lines[j])
                    if m3:
                        mean_ttft = m3.group(1)
                if not mean_tpot:
                    m4 = mean_tpot_re.search(lines[j])
                    if m4:
                        mean_tpot = m4.group(1)
                if all([total_token, output_token, mean_ttft, mean_tpot]):
                    break

            # ① 首条记录前插入空行
            # if not first_record_seen:
            #     results.append([''] * 7)
            #     first_record_seen = True

            # # ② 当批次大小变化时再次插入空行
            # if last_bs is not None and num_prompts != last_bs:
            #     results.append([''] * 7)

            # ③ 写入当前记录
            results.append([
                num_prompts,
                input_len,
                output_len,
                total_token,
                output_token,
                mean_ttft,
                mean_tpot
            ])
            last_bs = num_prompts
        i += 1

    # 写出 CSV
    with open(csv_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow([
            "num-prompts",
            "input-len",
            "output-len",
            "Total Token throughput (tok/s):",
            "Output token throughput (tok/s):",
            "Mean TTFT (ms):",
            "Mean TPOT (ms):"
        ])
        writer.writerows(results)

    return csv_file

def main() -> None:
    # 扫描当前目录下的所有 .log 文件
    log_files = [fn for fn in os.listdir('.') if fn.endswith('.log')]
    for log_file in log_files:
        csv_path = process_log_to_csv_with_blanks(log_file)
        print(f"{log_file} → {csv_path} 完成")

if __name__ == "__main__":
    main()
