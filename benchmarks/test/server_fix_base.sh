#!/bin/bash

MODEL="qwen"
TOKENIZER="/mnt/data/llm-models/qwen3/Qwen3-30B-A3B/"
IGNORE_EOS="--ignore-eos"
DATASET_NAME="random"
PORT=8161
LOGFILE="${MODEL}_fix_base.log"

# 文件锁，确保同一时间只有一个请求访问服务
LOCKFILE="/tmp/qwen3-30b_bench-fix_base.lock"

acquire_lock() {
  exec 200>"$LOCKFILE"
  flock -n 200 \
    && return 0 \
    || { echo "已有其他进程在运行，退出..."; exit 1; }
}

release_lock() {
  flock -u 200
  rm -f "$LOCKFILE"
}


COMBINATIONS=(
  "1 1 128"         # float16, batch=1, 输入1, 输出128
  "1 512 512"       # float16, batch=1, 输入512, 输出512
  "1 512 1024"      # float16, batch=1, 输入512, 输出1024
  "1 4096 1"        # float16, batch=1, 输入4096, 输出1
  "16 1 128"        # float16, batch=16, 输入1, 输出128
  "16 512 512"      # float16, batch=16, 输入512, 输出512
  "16 512 1024"     # float16, batch=16, 输入512, 输出1024
  "16 4096 1"       # float16, batch=16, 输入4096, 输出1
  "32 1 128"        # float16, batch=32, 输入1, 输出128
  "32 512 512"      # float16, batch=32, 输入512, 输出512
  "32 512 1024"     # float16, batch=32, 输入512, 输出1024
  "32 4096 1"       # float16, batch=32, 输入4096, 输出1
  "64 1 128"        # float16, batch=64, 输入1, 输出128
  "64 512 512"      # float16, batch=64, 输入512, 输出512
  "64 512 1024"     # float16, batch=64, 输入512, 输出1024
  "64 4096 1"       # float16, batch=64, 输入4096, 输出1
  "128 1 128"       # float16, batch=128, 输入1, 输出128
  "128 512 512"     # float16, batch=128, 输入512, 输出512
  "128 512 1024"    # float16, batch=128, 输入512, 输出1024
  "128 4096 1"      # float16, batch=128, 输入4096, 输出1
)

# 1. 先启动服务
echo "启动Qwen3-30B-A3B服务..."
HIP_VISIBLE_DEVICES=4,5 PYTHONPATH=/home/<USER>/ VLLM_USE_FLASH_ATTN_PA=1 \
  python -m vllm.entrypoints.openai.api_server \
  --model /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ \
  -tp 2 --dtype float16 --served-model-name qwen --block-size 64 --port $PORT --disable-log-requests --no-enable-prefix-caching --no-enable-chunked-prefill   \
  > qwen3-30b_server_fix_base_server.log 2>&1 &

SERVER_PID=$!

# 等待服务端口就绪
echo "等待服务端口 $PORT 就绪..."
# 使用curl替代nc，避免nc相关的not found打印
while ! curl -s --head http://localhost:$PORT/v1/models > /dev/null; do
  sleep 2
done
echo "服务已启动，开始发送请求..."

# 2. 依次发送请求，参数一一对应
for combination in "${COMBINATIONS[@]}"; do
  acquire_lock
  read -r VALUE RANDOM_INPUT_LEN RANDOM_OUTPUT_LEN <<< "$combination"
  echo "Running: num-prompts=$VALUE, max-concurrency=$VALUE, input-len=$RANDOM_INPUT_LEN, output-len=$RANDOM_OUTPUT_LEN" | tee -a $LOGFILE
  HIP_VISIBLE_DEVICES=4,5 python3 /home/<USER>/benchmarks/benchmark_serving.py \
    --model $MODEL \
    --tokenizer $TOKENIZER \
    --random-input-len $RANDOM_INPUT_LEN \
    --random-output-len $RANDOM_OUTPUT_LEN \
    --num-prompts $((VALUE * 10))\
    --max-concurrency $VALUE \
    $IGNORE_EOS \
    --dataset-name $DATASET_NAME \
    --port $PORT \
    >> $LOGFILE 2>&1
  echo "---------------------------------------------" | tee -a $LOGFILE
  release_lock
done

# 3. 结束后关闭服务
echo "测试完成，关闭服务..."
kill $SERVER_PID
