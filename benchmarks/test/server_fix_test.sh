#!/bin/bash

MODEL="qwen"
TOKENIZER="/mnt/data/llm-models/qwen3/Qwen3-30B-A3B/"
IGNORE_EOS="--ignore-eos"
DATASET_NAME="random"
BASE_PORT=8161
LOGFILE="${MODEL}_fix_base.log"

# 并行服务实例配置
NUM_INSTANCES=4
INSTANCE_PORTS=(8161 8162  8163 8164)
INSTANCE_GPUS=("4,5" "6,7" "0,1" "2,3")  # 每个实例使用的GPU设备

# 手动配置每个实例的完整启动命令
INSTANCE_COMMANDS=(

  # 实例 0: level 0 full cudagraph+inductor+triton fa
  "HIP_VISIBLE_DEVICES=4,5 PYTHONPATH=/home/<USER>/  python -m vllm.entrypoints.openai.api_server --model /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ -tp 2 --dtype float16 --served-model-name qwen --block-size 64 --port 8161 --disable-log-requests --no-enable-prefix-caching   --disable-log-requests --compilation-config '{\"level\": 0, \"use_inductor\": true, \"use_cudagraph\": true, \"full_cuda_graph\": true, \"debug_dump_path\": \"/tmp/vllm_debug_chunk\"}'"

  # 实例 1: level 1 full cudagraph+inductor+triton fa 
  "HIP_VISIBLE_DEVICES=6,7 PYTHONPATH=/home/<USER>/  python -m vllm.entrypoints.openai.api_server --model /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ -tp 2 --dtype float16 --served-model-name qwen --block-size 64 --port 8162 --disable-log-requests --no-enable-prefix-caching  --disable-log-requests --compilation-config '{\"level\": 1, \"use_inductor\": true, \"use_cudagraph\": true, \"full_cuda_graph\": true, \"debug_dump_path\": \"/tmp/vllm_debug_chunk\"}'"

  # 实例 2: level 2 full cudagraph+inductor+triton fa
  "HIP_VISIBLE_DEVICES=0,1 PYTHONPATH=/home/<USER>/ python -m vllm.entrypoints.openai.api_server --model /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ -tp 2 --dtype bfloat16 --served-model-name qwen --block-size 64 --port 8163 --disable-log-requests --no-enable-prefix-caching  --disable-log-requests --compilation-config '{\"level\": 2, \"use_inductor\": true, \"use_cudagraph\": true, \"full_cuda_graph\": true, \"debug_dump_path\": \"/tmp/vllm_debug_chunk\"}'"

  # 实例 3: full cudagraph+inductor+cutlass fa 复测
  "HIP_VISIBLE_DEVICES=2,3 PYTHONPATH=/home/<USER>/ VLLM_FLASH_ATTN_V1=1  python -m vllm.entrypoints.openai.api_server --model /mnt/data/llm-models/qwen3/Qwen3-30B-A3B/ -tp 2 --dtype float16 --served-model-name qwen --block-size 64 --port 8164 --disable-log-requests --no-enable-prefix-caching   --disable-log-requests --compilation-config '{\"level\": 3, \"use_inductor\": true, \"use_cudagraph\": true, \"full_cuda_graph\": true, \"debug_dump_path\": \"/tmp/vllm_debug_chunk\"}'"
)

SERVER_PIDS=()  # 存储所有服务进程ID

# 注意：由于每个实例现在都运行完整的测试序列，不再需要文件锁机制

# 清理函数，确保所有服务实例被正确关闭
cleanup() {
  echo "正在关闭所有服务实例..."
  for pid in "${SERVER_PIDS[@]}"; do
    if kill -0 "$pid" 2>/dev/null; then
      echo "关闭服务进程 $pid"
      kill "$pid"
    fi
  done
  # 等待进程完全关闭
  sleep 3
  for pid in "${SERVER_PIDS[@]}"; do
    if kill -0 "$pid" 2>/dev/null; then
      echo "强制关闭服务进程 $pid"
      kill -9 "$pid"
    fi
  done
}

# 设置信号处理，确保脚本退出时清理资源
trap cleanup EXIT INT TERM


COMBINATIONS=(
  "1 1 128"         # float16, batch=1, 输入1, 输出128
  "1 512 512"       # float16, batch=1, 输入512, 输出512
  "1 512 1024"      # float16, batch=1, 输入512, 输出1024
  "1 4096 1"        # float16, batch=1, 输入4096, 输出1
  "16 1 128"        # float16, batch=16, 输入1, 输出128
  "16 512 512"      # float16, batch=16, 输入512, 输出512
  "16 512 1024"     # float16, batch=16, 输入512, 输出1024
  "16 4096 1"       # float16, batch=16, 输入4096, 输出1
  "32 1 128"        # float16, batch=32, 输入1, 输出128
  "32 512 512"      # float16, batch=32, 输入512, 输出512
  "32 512 1024"     # float16, batch=32, 输入512, 输出1024
  "32 4096 1"       # float16, batch=32, 输入4096, 输出1
  "64 1 128"        # float16, batch=64, 输入1, 输出128
  "64 512 512"      # float16, batch=64, 输入512, 输出512
  "64 512 1024"     # float16, batch=64, 输入512, 输出1024
  "64 4096 1"       # float16, batch=64, 输入4096, 输出1
  "128 1 128"       # float16, batch=128, 输入1, 输出128
  "128 512 512"     # float16, batch=128, 输入512, 输出512
  "128 512 1024"    # float16, batch=128, 输入512, 输出1024
  "128 4096 1"      # float16, batch=128, 输入4096, 输出1
)

# 启动单个服务实例的函数（不等待）
start_server_instance_async() {
  local instance_id=$1
  local port=$2
  local command="${INSTANCE_COMMANDS[$instance_id]}"

  echo "启动服务实例 $instance_id (端口: $port):"
  echo "命令: $command"

  # 执行自定义命令并重定向输出
  eval "$command > qwen3-30b_server_fix_base_server_${instance_id}.log 2>&1 &"

  local server_pid=$!
  SERVER_PIDS+=($server_pid)
  echo "服务实例 $instance_id 已在后台启动 (PID: $server_pid)"
}

# 等待单个服务实例就绪的函数
wait_server_instance_ready() {
  local instance_id=$1
  local port=$2

  echo "等待服务实例 $instance_id 端口 $port 就绪..."
  while ! curl -s --head http://localhost:$port/v1/models > /dev/null; do
    sleep 2
  done
  echo "服务实例 $instance_id 已启动完成"
}

# 1. 同时启动所有服务实例（不等待）
echo "同时启动 $NUM_INSTANCES 个 Qwen3-30B-A3B 服务实例..."
for i in $(seq 0 $((NUM_INSTANCES-1))); do
  start_server_instance_async $i ${INSTANCE_PORTS[$i]}
done

echo "所有服务实例已在后台启动，等待就绪..."

# 2. 等待所有服务实例就绪
for i in $(seq 0 $((NUM_INSTANCES-1))); do
  wait_server_instance_ready $i ${INSTANCE_PORTS[$i]}
done

echo "所有服务实例已启动完成，开始并行发送请求..."

# 运行单个测试的函数
run_benchmark() {
  local combination="$1"
  local instance_id="$2"
  local port="${INSTANCE_PORTS[$instance_id]}"
  local gpu_devices="${INSTANCE_GPUS[$instance_id]}"

  read -r VALUE RANDOM_INPUT_LEN RANDOM_OUTPUT_LEN <<< "$combination"
  local test_name="batch${VALUE}_in${RANDOM_INPUT_LEN}_out${RANDOM_OUTPUT_LEN}"
  local instance_logfile="${MODEL}_fix_base_instance_${instance_id}.log"

  echo "实例 $instance_id: Running $test_name (端口: $port)" | tee -a $LOGFILE
  echo "实例 $instance_id: num-prompts=$VALUE, max-concurrency=$VALUE, input-len=$RANDOM_INPUT_LEN, output-len=$RANDOM_OUTPUT_LEN" | tee -a $instance_logfile
  echo "实例 $instance_id: 服务命令 - ${INSTANCE_COMMANDS[$instance_id]}" | tee -a $instance_logfile
  echo "DEBUG: instance_id=$instance_id, port=$port, INSTANCE_PORTS[${instance_id}]=${INSTANCE_PORTS[$instance_id]}" | tee -a $instance_logfile

  HIP_VISIBLE_DEVICES=$gpu_devices python3 /home/<USER>/benchmarks/benchmark_serving.py \
    --model $MODEL \
    --tokenizer $TOKENIZER \
    --random-input-len $RANDOM_INPUT_LEN \
    --random-output-len $RANDOM_OUTPUT_LEN \
    --num-prompts $VALUE \
    --max-concurrency $VALUE \
    $IGNORE_EOS \
    --dataset-name $DATASET_NAME \
    --port $port \
    >> $instance_logfile 2>&1

  echo "实例 $instance_id: 完成 $test_name" | tee -a $LOGFILE
  echo "---------------------------------------------" | tee -a $instance_logfile
}

# 2. 每个实例并行测试所有组合
echo "开始并行测试，每个实例都将测试所有组合..."

# 为每个实例启动完整的测试序列
INSTANCE_JOBS=()
for instance_id in $(seq 0 $((NUM_INSTANCES-1))); do
  echo "启动实例 $instance_id 的完整测试序列"

  # 在后台运行该实例的所有测试
  (
    for combination in "${COMBINATIONS[@]}"; do
      echo "实例 $instance_id: 开始测试 $combination"
      run_benchmark "$combination" $instance_id
      echo "实例 $instance_id: 完成测试 $combination"
    done
    echo "实例 $instance_id: 所有测试完成"
  ) &

  instance_job_pid=$!
  INSTANCE_JOBS+=($instance_job_pid)
  echo "实例 $instance_id 的测试序列已启动 (PID: $instance_job_pid)"
done

# 等待所有实例完成测试
echo "等待所有实例完成测试..."
for job_pid in "${INSTANCE_JOBS[@]}"; do
  wait $job_pid
  echo "实例测试进程 $job_pid 已完成"
done

echo "所有测试完成！"

# 3. 清理工作由trap处理，无需手动调用
